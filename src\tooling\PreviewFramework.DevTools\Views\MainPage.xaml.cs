using Microsoft.UI.Xaml.Controls;
using PreviewFramework.DevTools.ViewModels;
using PreviewFramework.DevTools.ViewModels.NavTree;

namespace PreviewFramework.DevTools.Views;

public sealed partial class MainPage : Page
{
    public MainPage()
    {
        this.InitializeComponent();
    }

    public MainPageViewModel? ViewModel => DataContext as MainPageViewModel;

    private void OnNavTreeItemInvoked(TreeView sender, TreeViewItemInvokedEventArgs args)
    {
        if (args.InvokedItem is NavTreeItemViewModel selectedItem)
        {
            // Check if this is a UIComponentViewModel with children (multiple previews)
            if (selectedItem is UIComponentViewModel uiComponentViewModel &&
                uiComponentViewModel.Children is not null &&
                uiComponentViewModel.Children.Count > 0)
            {
                // Find the TreeViewNode for this item by searching through the tree
                TreeViewNode? node = FindNodeByContent(sender.RootNodes, selectedItem);
                if (node is not null)
                {
                    // Expand the node if it's collapsed
                    if (!node.IsExpanded)
                    {
                        node.IsExpanded = true;
                    }

                    // Select the first child and invoke it
                    if (node.Children.Count > 0)
                    {
                        TreeViewNode firstChildNode = node.Children[0];
                        sender.SelectedNode = firstChildNode;

                        // Get the first child view model and invoke it
                        NavTreeItemViewModel firstChildViewModel = uiComponentViewModel.Children[0];
                        firstChildViewModel.OnItemInvoked();
                        return;
                    }
                }
            }

            // Default behavior for other items
            selectedItem.OnItemInvoked();
        }
    }

    private static TreeViewNode? FindNodeByContent(IList<TreeViewNode> nodes, object content)
    {
        foreach (TreeViewNode node in nodes)
        {
            if (ReferenceEquals(node.Content, content))
            {
                return node;
            }

            TreeViewNode? childResult = FindNodeByContent(node.Children, content);
            if (childResult is not null)
            {
                return childResult;
            }
        }
        return null;
    }
}
