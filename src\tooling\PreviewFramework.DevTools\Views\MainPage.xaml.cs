using PreviewFramework.DevTools.ViewModels;
using PreviewFramework.DevTools.ViewModels.NavTree;

namespace PreviewFramework.DevTools.Views;

public sealed partial class MainPage : Page
{
    public MainPage()
    {
        this.InitializeComponent();
    }

    public MainPageViewModel? ViewModel => DataContext as MainPageViewModel;

    private void OnNavTreeItemInvoked(TreeView sender, TreeViewItemInvokedEventArgs args)
    {
        if (args.InvokedItem is NavTreeItemViewModel selectedItem)
        {
            // Check if this is a UIComponentViewModel with children (multiple previews)
            if (selectedItem is UIComponentViewModel uiComponentViewModel &&
                uiComponentViewModel.Children is not null &&
                uiComponentViewModel.Children.Count > 0)
            {
                sender.SelectedItem = uiComponentViewModel.Children.First();

                return;

                // Get the container from the invoked item and cast in one line
                TreeViewItem? treeViewItem = sender.ContainerFromItem(args.InvokedItem) as TreeViewItem;

                if (treeViewItem is not null)
                {
                    // Get the node from the container
                    TreeViewNode? node = sender.NodeFromContainer(treeViewItem);

                    if (node is not null)
                    {
                        // Expand the node if it's collapsed
                        if (!node.IsExpanded)
                        {
                            node.IsExpanded = true;
                        }

                        // Select the first child and invoke it
                        if (node.Children.Count > 0)
                        {
                            TreeViewNode firstChildNode = node.Children[0];

                            // Clear current selection and set new selection
                            sender.SelectedNode = null;
                            sender.SelectedNode = firstChildNode;

                            // Also try setting the container selection
                            TreeViewItem? firstChildContainer = sender.ContainerFromNode(firstChildNode) as TreeViewItem;
                            if (firstChildContainer is not null)
                            {
                                // Clear parent selection first
                                if (treeViewItem is not null)
                                {
                                    treeViewItem.IsSelected = false;
                                }
                                firstChildContainer.IsSelected = true;
                            }

                            // Get the first child view model and invoke it
                            NavTreeItemViewModel firstChildViewModel = uiComponentViewModel.Children[0];
                            firstChildViewModel.OnItemInvoked();
                            return;
                        }
                    }
                }
            }

            // Default behavior for other items
            selectedItem.OnItemInvoked();
        }
    }
}
