using PreviewFramework.Tooling;

namespace PreviewFramework.DevTools.ViewModels.NavTree;

public class UIComponentViewModel(UIComponentTooling uiComponent) : NavTreeItemViewModel
{
    public override string DisplayName => uiComponent.DisplayName;

    public override string PathIcon => uiComponent.PathIcon;

    public override IReadOnlyList<NavTreeItemViewModel>? Children { get; } =
        uiComponent.HasMultiplePreviews ?
            uiComponent.Previews.Select(preview => new PreviewViewModel(uiComponent, preview)).ToList() :
            null;

    public override void OnItemInvoked()
    {
        if (uiComponent.HasSinglePreview)
        {
            // Navigate to the preview, for all app connections that have the preview
            DevToolsManager.Instance.MainPageViewModel.CurrentApp?.NavigateToPreview(uiComponent, uiComponent.DefaultPreview);
        }
        else if (uiComponent.HasMultiplePreviews)
        {
            // For components with multiple previews:
            // - The tree node should be expanded (handled by UI)
            // - Navigate to the first preview
            if (uiComponent.Previews.Count > 0)
            {
                PreviewTooling firstPreview = uiComponent.Previews[0];
                DevToolsManager.Instance.MainPageViewModel.CurrentApp?.NavigateToPreview(uiComponent, firstPreview);
            }
        }
    }
}
